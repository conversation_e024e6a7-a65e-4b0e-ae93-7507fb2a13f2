# WeThinkCode_ Test Automation Framework

A robust, scalable test automation framework for the WeThinkCode_ website using Java, Selenium WebDriver 4+, TestNG, and Maven.

## 🚀 Features

- **Page Object Model (POM)** design pattern for maintainable code
- **Cross-browser testing** (Chrome, Firefox)
- **Mobile responsiveness testing** with device emulation
- **WebDriverManager** for automatic driver setup
- **Parallel test execution** support
- **Comprehensive reporting** with ExtentReports
- **CI/CD ready** with Maven profiles
- **Configurable environments** via properties files

## 📁 Project Structure

```
src/
├── main/java/utils/          → Utility classes (DriverFactory, ConfigReader)
└── test/java/
    ├── pages/                → Page Object Model classes
    ├── tests/                → TestNG test classes
    └── listeners/            → Test listeners for reporting
    └── resources/            → Configuration files
pom.xml                       → Maven dependencies and plugins
testng.xml                    → TestNG suite configuration
README.md                     → This file
.gitignore                    → Git ignore rules
```

## 🛠️ Prerequisites

- **Java 11** or higher
- **Maven 3.6+**
- **Git**
- **Chrome/Firefox** browsers installed

## ⚡ Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd wtc-user-journey-testing
```

### 2. Install Dependencies
```bash
mvn clean install
```

### 3. Run Tests

#### Run all tests (Chrome by default):
```bash
mvn clean test
```

#### Run tests with specific browser:
```bash
# Chrome
mvn clean test -Dbrowser=chrome

# Firefox
mvn clean test -Dbrowser=firefox

# Mobile (iPhone emulation)
mvn clean test -Dbrowser=mobile
```

#### Run tests with Maven profiles:
```bash
# Chrome profile
mvn clean test -Pchrome

# Firefox profile
mvn clean test -Pfirefox

# Mobile profile
mvn clean test -Pmobile
```

#### Run specific test class:
```bash
mvn clean test -Dtest=SmokeTests
```

## 🧪 Test Categories

### Smoke Tests (`SmokeTests.java`)
- Homepage loading validation
- Key elements visibility
- Basic navigation testing
- Apply Now functionality
- About page navigation
- Contact page navigation

### Future Test Categories
- **Form Tests**: Application forms, contact forms
- **Responsive Tests**: Mobile device testing
- **Integration Tests**: End-to-end user journeys
- **Performance Tests**: Page load times

## 📊 Reporting

Test reports are generated in:
- **TestNG Reports**: `target/surefire-reports/`
- **ExtentReports**: `reports/extent-report.html` (when configured)

## ⚙️ Configuration

### Environment Configuration (`src/test/resources/config.properties`)
```properties
# Application URL
base.url=https://www.wethinkcode.co.za/

# Browser Configuration
browser=chrome
timeout=10

# Test Data
test.email=<EMAIL>
test.name=Test User
```

### Browser Configuration
- **Chrome**: Optimized with performance flags
- **Firefox**: Standard configuration
- **Mobile**: iPhone 12 Pro emulation

## 🔧 Utilities

### DriverFactory
- Thread-safe WebDriver management
- Automatic driver setup with WebDriverManager
- Support for multiple browsers and mobile emulation

### ConfigReader
- Centralized configuration management
- Environment-specific settings
- Runtime parameter override support

## 📱 Mobile Testing

Mobile tests use Chrome's device emulation:
```java
// iPhone 12 Pro emulation is configured by default
mvn clean test -Dbrowser=mobile
```

## 🚀 CI/CD Integration

### GitHub Actions (Future Enhancement)
```yaml
# Example workflow for CI/CD
name: Test Automation
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 11
        uses: actions/setup-java@v2
        with:
          java-version: '11'
      - name: Run tests
        run: mvn clean test
```

## 🐛 Troubleshooting

### Common Issues

1. **WebDriver not found**
   - Solution: WebDriverManager handles this automatically

2. **Element not found**
   - Check if page has loaded completely
   - Verify locator strategies in Page Objects
   - Increase timeout if needed

3. **Tests failing on different environments**
   - Update `config.properties` for environment-specific URLs
   - Use environment profiles

### Debug Mode
```bash
# Run with verbose logging
mvn clean test -Dlog4j.configuration=debug
```

## 📈 Best Practices Implemented

- ✅ Page Object Model design pattern
- ✅ Explicit waits over implicit waits
- ✅ Thread-safe WebDriver management
- ✅ Configurable test data
- ✅ Meaningful test names and descriptions
- ✅ Proper exception handling
- ✅ Clean code principles

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📝 Test Execution Status

| Test Category | Status | Browser Support |
|---------------|--------|-----------------|
| Smoke Tests   | ✅ Ready | Chrome, Firefox, Mobile |
| Form Tests    | 🚧 Planned | All |
| Mobile Tests  | ✅ Ready | Chrome (Emulation) |
| Cross-browser | ✅ Ready | Chrome, Firefox |

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review test logs in `target/surefire-reports/`
3. Create an issue in the repository

---

**Framework Version**: 1.0.0  
**Last Updated**: 2025-09-09  
**Maintained by**: Test Automation Team
