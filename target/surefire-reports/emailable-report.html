<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "https://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
<title>TestNG Report</title>
<style type="text/css">table {margin-bottom:10px;border-collapse:collapse;empty-cells:show}th,td {border:1px solid #009;padding:.25em .5em}th {vertical-align:bottom}td {vertical-align:top}table a {font-weight:bold}.stripe td {background-color: #E6EBF9}.num {text-align:right}.passedodd td {background-color: #3F3}.passedeven td {background-color: #0A0}.skippedodd td {background-color: #DDD}.skippedeven td {background-color: #CCC}.failedodd td,.attn {background-color: #F33}.failedeven td,.stripe .attn {background-color: #D00}.stacktrace {white-space:pre;font-family:monospace}.totop {font-size:85%;text-align:center;border-bottom:2px solid #000}.invisible {display:none}</style>
</head>
<body>
<table>
<tr><th>Test</th><th># Passed</th><th># Skipped</th><th># Retried</th><th># Failed</th><th>Time (ms)</th><th>Included Groups</th><th>Excluded Groups</th></tr>
<tr><th colspan="8">Surefire suite</th></tr>
<tr><td><a href="#t0">Surefire test</a></td><td class="num">11</td><td class="num">0</td><td class="num">0</td><td class="num attn">1</td><td class="num">132</td><td></td><td></td></tr>
</table>
<table id='summary'><thead><tr><th>Class</th><th>Method</th><th>Start</th><th>Time (ms)</th></tr></thead><tbody><tr><th colspan="4">Surefire suite</th></tr></tbody><tbody id="t0"><tr><th colspan="4">Surefire test &#8212; failed</th></tr><tr class="failedeven"><td rowspan="1">tests.ConfigurationTests</td><td><a href="#m0">testBrowserConfiguration</a></td><td rowspan="1">1757421467980</td><td rowspan="1">10</td></tr><tr><th colspan="4">Surefire test &#8212; passed</th></tr><tr class="passedeven"><td rowspan="5">tests.ConfigurationTests</td><td><a href="#m1">testConfigurationLoading</a></td><td rowspan="1">1757421467966</td><td rowspan="1">9</td></tr><tr class="passedeven"><td><a href="#m2">testEnvironmentConfiguration</a></td><td rowspan="1">1757421468003</td><td rowspan="1">2</td></tr><tr class="passedeven"><td><a href="#m3">testSystemPropertiesOverride</a></td><td rowspan="1">1757421468009</td><td rowspan="1">2</td></tr><tr class="passedeven"><td><a href="#m4">testTestDataConfiguration</a></td><td rowspan="1">1757421468006</td><td rowspan="1">1</td></tr><tr class="passedeven"><td><a href="#m5">testTimeoutConfiguration</a></td><td rowspan="1">1757421467997</td><td rowspan="1">4</td></tr><tr class="passedodd"><td rowspan="6">tests.FrameworkValidationTests</td><td><a href="#m6">testConfigReaderUtility</a></td><td rowspan="1">1757421468013</td><td rowspan="1">6</td></tr><tr class="passedodd"><td><a href="#m7">testDriverFactoryMethods</a></td><td rowspan="1">1757421468025</td><td rowspan="1">4</td></tr><tr class="passedodd"><td><a href="#m8">testFrameworkReadiness</a></td><td rowspan="1">1757421468056</td><td rowspan="1">3</td></tr><tr class="passedodd"><td><a href="#m9">testHomePagePOMStructure</a></td><td rowspan="1">1757421468021</td><td rowspan="1">3</td></tr><tr class="passedodd"><td><a href="#m10">testMavenDependencies</a></td><td rowspan="1">1757421468031</td><td rowspan="1">20</td></tr><tr class="passedodd"><td><a href="#m11">testProjectStructure</a></td><td rowspan="1">1757421468054</td><td rowspan="1">1</td></tr></tbody>
</table>
<h2>Surefire test</h2><h3 id="m0">tests.ConfigurationTests#testBrowserConfiguration</h3><table class="result"><tr><th>Exception</th></tr><tr><td><div class="stacktrace">java.lang.AssertionError: Browser configuration should not be empty expected [false] but found [true]
	at tests.ConfigurationTests.testBrowserConfiguration(ConfigurationTests.java:28)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
... Removed 33 stack frames</div></td></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m1">tests.ConfigurationTests#testConfigurationLoading</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m2">tests.ConfigurationTests#testEnvironmentConfiguration</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m3">tests.ConfigurationTests#testSystemPropertiesOverride</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m4">tests.ConfigurationTests#testTestDataConfiguration</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m5">tests.ConfigurationTests#testTimeoutConfiguration</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m6">tests.FrameworkValidationTests#testConfigReaderUtility</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m7">tests.FrameworkValidationTests#testDriverFactoryMethods</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m8">tests.FrameworkValidationTests#testFrameworkReadiness</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m9">tests.FrameworkValidationTests#testHomePagePOMStructure</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m10">tests.FrameworkValidationTests#testMavenDependencies</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
<h3 id="m11">tests.FrameworkValidationTests#testProjectStructure</h3><table class="result"><tr><th class="invisible"/></tr></table><p class="totop"><a href="#summary">back to summary</a></p>
</body>
</html>
