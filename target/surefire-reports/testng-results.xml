<?xml version="1.0" encoding="UTF-8"?>
<testng-results ignored="0" total="12" passed="11" failed="1" skipped="0">
  <reporter-output>
  </reporter-output>
  <suite started-at="2025-09-09T14:37:47 SAST" name="Surefire suite" finished-at="2025-09-09T14:37:48 SAST" duration-ms="132">
    <groups>
    </groups>
    <test started-at="2025-09-09T14:37:47 SAST" name="Surefire test" finished-at="2025-09-09T14:37:48 SAST" duration-ms="132">
      <class name="tests.ConfigurationTests">
        <test-method signature="testConfigurationLoading()[pri:1, instance:tests.ConfigurationTests@436a4e4b]" started-at="2025-09-09T14:37:47 SAST" name="testConfigurationLoading" description="Verify configuration properties are loaded" finished-at="2025-09-09T14:37:47 SAST" duration-ms="9" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testConfigurationLoading -->
        <test-method signature="testBrowserConfiguration()[pri:2, instance:tests.ConfigurationTests@436a4e4b]" started-at="2025-09-09T14:37:47 SAST" name="testBrowserConfiguration" description="Verify browser configuration" finished-at="2025-09-09T14:37:47 SAST" duration-ms="10" status="FAIL">
          <exception class="java.lang.AssertionError">
            <message>
              <![CDATA[Browser configuration should not be empty expected [false] but found [true]]]>
            </message>
            <full-stacktrace>
              <![CDATA[java.lang.AssertionError: Browser configuration should not be empty expected [false] but found [true]
at org.testng.Assert.fail(Assert.java:111)
at org.testng.Assert.failNotEquals(Assert.java:1578)
at org.testng.Assert.assertFalse(Assert.java:79)
at tests.ConfigurationTests.testBrowserConfiguration(ConfigurationTests.java:28)
at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
at java.base/java.lang.reflect.Method.invoke(Method.java:580)
at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)
at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:664)
at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:227)
at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)
at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:957)
at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:200)
at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
at org.testng.TestRunner.privateRun(TestRunner.java:848)
at org.testng.TestRunner.run(TestRunner.java:621)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
at org.testng.SuiteRunner.run(SuiteRunner.java:336)
at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
at org.testng.TestNG.runSuitesSequentially(TestNG.java:1280)
at org.testng.TestNG.runSuitesLocally(TestNG.java:1200)
at org.testng.TestNG.runSuites(TestNG.java:1114)
at org.testng.TestNG.run(TestNG.java:1082)
at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:155)
at org.apache.maven.surefire.testng.TestNGDirectoryTestSuite.executeMulti(TestNGDirectoryTestSuite.java:169)
at org.apache.maven.surefire.testng.TestNGDirectoryTestSuite.execute(TestNGDirectoryTestSuite.java:88)
at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:137)
at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
]]>
            </full-stacktrace>
          </exception> <!-- java.lang.AssertionError -->
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testBrowserConfiguration -->
        <test-method signature="testTimeoutConfiguration()[pri:3, instance:tests.ConfigurationTests@436a4e4b]" started-at="2025-09-09T14:37:47 SAST" name="testTimeoutConfiguration" description="Verify timeout configuration" finished-at="2025-09-09T14:37:48 SAST" duration-ms="4" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testTimeoutConfiguration -->
        <test-method signature="testEnvironmentConfiguration()[pri:4, instance:tests.ConfigurationTests@436a4e4b]" started-at="2025-09-09T14:37:48 SAST" name="testEnvironmentConfiguration" description="Verify environment configuration" finished-at="2025-09-09T14:37:48 SAST" duration-ms="2" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testEnvironmentConfiguration -->
        <test-method signature="testTestDataConfiguration()[pri:5, instance:tests.ConfigurationTests@436a4e4b]" started-at="2025-09-09T14:37:48 SAST" name="testTestDataConfiguration" description="Verify test data configuration" finished-at="2025-09-09T14:37:48 SAST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testTestDataConfiguration -->
        <test-method signature="testSystemPropertiesOverride()[pri:6, instance:tests.ConfigurationTests@436a4e4b]" started-at="2025-09-09T14:37:48 SAST" name="testSystemPropertiesOverride" description="Verify system properties override" finished-at="2025-09-09T14:37:48 SAST" duration-ms="2" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testSystemPropertiesOverride -->
      </class> <!-- tests.ConfigurationTests -->
      <class name="tests.FrameworkValidationTests">
        <test-method signature="testConfigReaderUtility()[pri:1, instance:tests.FrameworkValidationTests@3e3047e6]" started-at="2025-09-09T14:37:48 SAST" name="testConfigReaderUtility" description="Verify ConfigReader utility class" finished-at="2025-09-09T14:37:48 SAST" duration-ms="6" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testConfigReaderUtility -->
        <test-method signature="testHomePagePOMStructure()[pri:2, instance:tests.FrameworkValidationTests@3e3047e6]" started-at="2025-09-09T14:37:48 SAST" name="testHomePagePOMStructure" description="Verify HomePage POM class structure" finished-at="2025-09-09T14:37:48 SAST" duration-ms="3" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testHomePagePOMStructure -->
        <test-method signature="testDriverFactoryMethods()[pri:3, instance:tests.FrameworkValidationTests@3e3047e6]" started-at="2025-09-09T14:37:48 SAST" name="testDriverFactoryMethods" description="Verify DriverFactory class methods" finished-at="2025-09-09T14:37:48 SAST" duration-ms="4" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testDriverFactoryMethods -->
        <test-method signature="testMavenDependencies()[pri:4, instance:tests.FrameworkValidationTests@3e3047e6]" started-at="2025-09-09T14:37:48 SAST" name="testMavenDependencies" description="Verify Maven dependencies are available" finished-at="2025-09-09T14:37:48 SAST" duration-ms="20" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testMavenDependencies -->
        <test-method signature="testProjectStructure()[pri:5, instance:tests.FrameworkValidationTests@3e3047e6]" started-at="2025-09-09T14:37:48 SAST" name="testProjectStructure" description="Verify project structure" finished-at="2025-09-09T14:37:48 SAST" duration-ms="1" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testProjectStructure -->
        <test-method signature="testFrameworkReadiness()[pri:6, instance:tests.FrameworkValidationTests@3e3047e6]" started-at="2025-09-09T14:37:48 SAST" name="testFrameworkReadiness" description="Verify test framework is ready" finished-at="2025-09-09T14:37:48 SAST" duration-ms="3" status="PASS">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testFrameworkReadiness -->
      </class> <!-- tests.FrameworkValidationTests -->
    </test> <!-- Surefire test -->
  </suite> <!-- Surefire suite -->
</testng-results>
