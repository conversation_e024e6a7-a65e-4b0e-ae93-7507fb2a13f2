<table border='1'>
<tr>
<th>Class name</th>
<th>Method name</th>
<th>Groups</th>
</tr><tr>
<td>tests.ConfigurationTests</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>testConfigurationLoading</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>testTestDataConfiguration</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>testBrowserConfiguration</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>testSystemPropertiesOverride</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>testTimeoutConfiguration</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>testEnvironmentConfiguration</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
<tr>
<td>tests.FrameworkValidationTests</td>
<td>&nbsp;</td><td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@Test</td>
</tr>
<tr>
<td>&nbsp;</td>
<td>testDriverFactoryMethods</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>testFrameworkReadiness</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>testConfigReaderUtility</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>testHomePagePOMStructure</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>testProjectStructure</td>
<td>&nbsp;</td></tr>
<tr>
<td>&nbsp;</td>
<td>testMavenDependencies</td>
<td>&nbsp;</td></tr>
<tr>
<td align='center' colspan='3'>@BeforeClass</td>
</tr>
<tr>
<td align='center' colspan='3'>@BeforeMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterMethod</td>
</tr>
<tr>
<td align='center' colspan='3'>@AfterClass</td>
</tr>
</table>
