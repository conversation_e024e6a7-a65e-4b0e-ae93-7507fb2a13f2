<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>Surefire suite</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="f4f06f">  <td>25/09/09 14:37:48</td>   <td>0</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="FrameworkValidationTests.testProjectStructure()[pri:5, instance:tests.FrameworkValidationTests@3e3047e6]">testProjectStructure</td> 
  <td>main@1704856573</td>   <td></td> </tr>
<tr bgcolor="a29b66">  <td>25/09/09 14:37:48</td>   <td>-48</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="ConfigurationTests.testTestDataConfiguration()[pri:5, instance:tests.ConfigurationTests@436a4e4b]">testTestDataConfiguration</td> 
  <td>main@1704856573</td>   <td></td> </tr>
<tr bgcolor="f4f06f">  <td>25/09/09 14:37:48</td>   <td>-33</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="FrameworkValidationTests.testHomePagePOMStructure()[pri:2, instance:tests.FrameworkValidationTests@3e3047e6]">testHomePagePOMStructure</td> 
  <td>main@1704856573</td>   <td></td> </tr>
<tr bgcolor="a29b66">  <td>25/09/09 14:37:47</td>   <td>-89</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="ConfigurationTests.testConfigurationLoading()[pri:1, instance:tests.ConfigurationTests@436a4e4b]">testConfigurationLoading</td> 
  <td>main@1704856573</td>   <td></td> </tr>
<tr bgcolor="a29b66">  <td>25/09/09 14:37:48</td>   <td>-51</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="ConfigurationTests.testEnvironmentConfiguration()[pri:4, instance:tests.ConfigurationTests@436a4e4b]">testEnvironmentConfiguration</td> 
  <td>main@1704856573</td>   <td></td> </tr>
<tr bgcolor="f4f06f">  <td>25/09/09 14:37:48</td>   <td>-41</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="FrameworkValidationTests.testConfigReaderUtility()[pri:1, instance:tests.FrameworkValidationTests@3e3047e6]">testConfigReaderUtility</td> 
  <td>main@1704856573</td>   <td></td> </tr>
<tr bgcolor="f4f06f">  <td>25/09/09 14:37:48</td>   <td>-29</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="FrameworkValidationTests.testDriverFactoryMethods()[pri:3, instance:tests.FrameworkValidationTests@3e3047e6]">testDriverFactoryMethods</td> 
  <td>main@1704856573</td>   <td></td> </tr>
<tr bgcolor="a29b66">  <td>25/09/09 14:37:47</td>   <td>-74</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="ConfigurationTests.testBrowserConfiguration()[pri:2, instance:tests.ConfigurationTests@436a4e4b]">testBrowserConfiguration</td> 
  <td>main@1704856573</td>   <td></td> </tr>
<tr bgcolor="a29b66">  <td>25/09/09 14:37:47</td>   <td>-57</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="ConfigurationTests.testTimeoutConfiguration()[pri:3, instance:tests.ConfigurationTests@436a4e4b]">testTimeoutConfiguration</td> 
  <td>main@1704856573</td>   <td></td> </tr>
<tr bgcolor="a29b66">  <td>25/09/09 14:37:48</td>   <td>-45</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="ConfigurationTests.testSystemPropertiesOverride()[pri:6, instance:tests.ConfigurationTests@436a4e4b]">testSystemPropertiesOverride</td> 
  <td>main@1704856573</td>   <td></td> </tr>
<tr bgcolor="f4f06f">  <td>25/09/09 14:37:48</td>   <td>2</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="FrameworkValidationTests.testFrameworkReadiness()[pri:6, instance:tests.FrameworkValidationTests@3e3047e6]">testFrameworkReadiness</td> 
  <td>main@1704856573</td>   <td></td> </tr>
<tr bgcolor="f4f06f">  <td>25/09/09 14:37:48</td>   <td>-23</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="FrameworkValidationTests.testMavenDependencies()[pri:4, instance:tests.FrameworkValidationTests@3e3047e6]">testMavenDependencies</td> 
  <td>main@1704856573</td>   <td></td> </tr>
</table>
