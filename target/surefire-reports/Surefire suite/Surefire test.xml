<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite ignored="0" hostname="<PERSON><PERSON><PERSON>" failures="1" tests="12" name="Surefire test" time="0.132" errors="0" timestamp="2025-09-09T14:37:48 SAST">
  <testcase classname="tests.ConfigurationTests" name="testConfigurationLoading" time="0.009"/>
  <testcase classname="tests.ConfigurationTests" name="testBrowserConfiguration" time="0.01">
    <failure type="java.lang.AssertionError" message="Browser configuration should not be empty expected [false] but found [true]">
      <![CDATA[java.lang.AssertionError: Browser configuration should not be empty expected [false] but found [true]
at tests.ConfigurationTests.testBrowserConfiguration(ConfigurationTests.java:28)
at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
... Removed 33 stack frames]]>
    </failure>
  </testcase> <!-- testBrowserConfiguration -->
  <testcase classname="tests.ConfigurationTests" name="testTimeoutConfiguration" time="0.004"/>
  <testcase classname="tests.ConfigurationTests" name="testEnvironmentConfiguration" time="0.002"/>
  <testcase classname="tests.ConfigurationTests" name="testTestDataConfiguration" time="0.001"/>
  <testcase classname="tests.ConfigurationTests" name="testSystemPropertiesOverride" time="0.002"/>
  <testcase classname="tests.FrameworkValidationTests" name="testConfigReaderUtility" time="0.006"/>
  <testcase classname="tests.FrameworkValidationTests" name="testHomePagePOMStructure" time="0.003"/>
  <testcase classname="tests.FrameworkValidationTests" name="testDriverFactoryMethods" time="0.004"/>
  <testcase classname="tests.FrameworkValidationTests" name="testMavenDependencies" time="0.02"/>
  <testcase classname="tests.FrameworkValidationTests" name="testProjectStructure" time="0.001"/>
  <testcase classname="tests.FrameworkValidationTests" name="testFrameworkReadiness" time="0.003"/>
</testsuite> <!-- Surefire test -->
