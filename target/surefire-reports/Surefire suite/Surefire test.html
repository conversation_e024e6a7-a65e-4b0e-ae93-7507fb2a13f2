<html>
<head>
<title>TestNG:  Surefire test</title>
<link href="../testng.css" rel="stylesheet" type="text/css" />
<link href="../my-testng.css" rel="stylesheet" type="text/css" />

<style type="text/css">
.log { display: none;} 
.stack-trace { display: none;} 
</style>
<script type="text/javascript">
<!--
function flip(e) {
  current = e.style.display;
  if (current == 'block') {
    e.style.display = 'none';
    return 0;
  }
  else {
    e.style.display = 'block';
    return 1;
  }
}

function toggleBox(szDivId, elem, msg1, msg2)
{
  var res = -1;  if (document.getElementById) {
    res = flip(document.getElementById(szDivId));
  }
  else if (document.all) {
    // this is the way old msie versions work
    res = flip(document.all[szDivId]);
  }
  if(elem) {
    if(res == 0) elem.innerHTML = msg1; else elem.innerHTML = msg2;
  }

}

function toggleAllBoxes() {
  if (document.getElementsByTagName) {
    d = document.getElementsByTagName('div');
    for (i = 0; i < d.length; i++) {
      if (d[i].className == 'log') {
        flip(d[i]);
      }
    }
  }
}

// -->
</script>

</head>
<body>
<h2 align='center'>Surefire test</h2><table border='1' align="center">
<tr>
<td>Tests passed/Failed/Skipped:</td><td>11/1/0</td>
</tr><tr>
<td>Started on:</td><td>Tue Sep 09 14:37:47 SAST 2025</td>
</tr>
<tr><td>Total time:</td><td>0 seconds (132 ms)</td>
</tr><tr>
<td>Included groups:</td><td></td>
</tr><tr>
<td>Excluded groups:</td><td></td>
</tr>
</table><p/>
<small><i>(Hover the method name to see the test class name)</i></small><p/>
<table width='100%' border='1' class='invocation-failed'>
<tr><td colspan='4' align='center'><b>FAILED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td><b>Attribute(s)</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='tests.ConfigurationTests.testBrowserConfiguration()'><b>testBrowserConfiguration</b><br>Test class: tests.ConfigurationTests<br>Test method: Verify browser configuration</td>
<td><div><pre>java.lang.AssertionError: Browser configuration should not be empty expected [false] but found [true]
	at tests.ConfigurationTests.testBrowserConfiguration(ConfigurationTests.java:28)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
... Removed 33 stack frames</pre></div><a href='#' onClick='toggleBox("stack-trace1525919705", this, "Click to show all stack frames", "Click to hide stack frames")'>Click to show all stack frames</a>
<div class='stack-trace' id='stack-trace1525919705'><pre>java.lang.AssertionError: Browser configuration should not be empty expected [false] but found [true]
	at org.testng.Assert.fail(Assert.java:111)
	at org.testng.Assert.failNotEquals(Assert.java:1578)
	at org.testng.Assert.assertFalse(Assert.java:79)
	at tests.ConfigurationTests.testBrowserConfiguration(ConfigurationTests.java:28)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)
	at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:664)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:227)
	at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)
	at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:957)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:200)
	at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
	at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.testng.TestRunner.privateRun(TestRunner.java:848)
	at org.testng.TestRunner.run(TestRunner.java:621)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
	at org.testng.SuiteRunner.run(SuiteRunner.java:336)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1280)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1200)
	at org.testng.TestNG.runSuites(TestNG.java:1114)
	at org.testng.TestNG.run(TestNG.java:1082)
	at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:155)
	at org.apache.maven.surefire.testng.TestNGDirectoryTestSuite.executeMulti(TestNGDirectoryTestSuite.java:169)
	at org.apache.maven.surefire.testng.TestNGDirectoryTestSuite.execute(TestNGDirectoryTestSuite.java:88)
	at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:137)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
</pre></div></td>
<td>0</td>
<td>tests.ConfigurationTests@436a4e4b</td></tr>
</table><p>
<table width='100%' border='1' class='invocation-passed'>
<tr><td colspan='4' align='center'><b>PASSED TESTS</b></td></tr>
<tr><td><b>Test method</b></td>
<td><b>Attribute(s)</b></td>
<td width="30%"><b>Exception</b></td>
<td width="10%"><b>Time (seconds)</b></td>
<td><b>Instance</b></td>
</tr>
<tr>
<td title='tests.FrameworkValidationTests.testProjectStructure()'><b>testProjectStructure</b><br>Test class: tests.FrameworkValidationTests<br>Test method: Verify project structure</td>
<td></td>
<td>0</td>
<td>tests.FrameworkValidationTests@3e3047e6</td></tr>
<tr>
<td title='tests.ConfigurationTests.testTestDataConfiguration()'><b>testTestDataConfiguration</b><br>Test class: tests.ConfigurationTests<br>Test method: Verify test data configuration</td>
<td></td>
<td>0</td>
<td>tests.ConfigurationTests@436a4e4b</td></tr>
<tr>
<td title='tests.FrameworkValidationTests.testHomePagePOMStructure()'><b>testHomePagePOMStructure</b><br>Test class: tests.FrameworkValidationTests<br>Test method: Verify HomePage POM class structure</td>
<td></td>
<td>0</td>
<td>tests.FrameworkValidationTests@3e3047e6</td></tr>
<tr>
<td title='tests.ConfigurationTests.testConfigurationLoading()'><b>testConfigurationLoading</b><br>Test class: tests.ConfigurationTests<br>Test method: Verify configuration properties are loaded</td>
<td></td>
<td>0</td>
<td>tests.ConfigurationTests@436a4e4b</td></tr>
<tr>
<td title='tests.ConfigurationTests.testEnvironmentConfiguration()'><b>testEnvironmentConfiguration</b><br>Test class: tests.ConfigurationTests<br>Test method: Verify environment configuration</td>
<td></td>
<td>0</td>
<td>tests.ConfigurationTests@436a4e4b</td></tr>
<tr>
<td title='tests.FrameworkValidationTests.testConfigReaderUtility()'><b>testConfigReaderUtility</b><br>Test class: tests.FrameworkValidationTests<br>Test method: Verify ConfigReader utility class</td>
<td></td>
<td>0</td>
<td>tests.FrameworkValidationTests@3e3047e6</td></tr>
<tr>
<td title='tests.FrameworkValidationTests.testDriverFactoryMethods()'><b>testDriverFactoryMethods</b><br>Test class: tests.FrameworkValidationTests<br>Test method: Verify DriverFactory class methods</td>
<td></td>
<td>0</td>
<td>tests.FrameworkValidationTests@3e3047e6</td></tr>
<tr>
<td title='tests.ConfigurationTests.testTimeoutConfiguration()'><b>testTimeoutConfiguration</b><br>Test class: tests.ConfigurationTests<br>Test method: Verify timeout configuration</td>
<td></td>
<td>0</td>
<td>tests.ConfigurationTests@436a4e4b</td></tr>
<tr>
<td title='tests.ConfigurationTests.testSystemPropertiesOverride()'><b>testSystemPropertiesOverride</b><br>Test class: tests.ConfigurationTests<br>Test method: Verify system properties override</td>
<td></td>
<td>0</td>
<td>tests.ConfigurationTests@436a4e4b</td></tr>
<tr>
<td title='tests.FrameworkValidationTests.testFrameworkReadiness()'><b>testFrameworkReadiness</b><br>Test class: tests.FrameworkValidationTests<br>Test method: Verify test framework is ready</td>
<td></td>
<td>0</td>
<td>tests.FrameworkValidationTests@3e3047e6</td></tr>
<tr>
<td title='tests.FrameworkValidationTests.testMavenDependencies()'><b>testMavenDependencies</b><br>Test class: tests.FrameworkValidationTests<br>Test method: Verify Maven dependencies are available</td>
<td></td>
<td>0</td>
<td>tests.FrameworkValidationTests@3e3047e6</td></tr>
</table><p>
</body>
</html>