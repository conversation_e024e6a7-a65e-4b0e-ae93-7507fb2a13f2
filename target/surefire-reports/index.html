<!DOCTYPE html>

<html>
  <head>
  <meta charset='utf-8'>
  <title>TestNG reports</title>

    <link type="text/css" href="testng-reports1.css" rel="stylesheet" id="ultra" />
    <link type="text/css" href="testng-reports.css" rel="stylesheet" id="retro" disabled="false"/>
    <script type="text/javascript" src="jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="testng-reports.js"></script>
    <script type="text/javascript" src="https://www.google.com/jsapi"></script>
    <script type='text/javascript'>
      google.load('visualization', '1', {packages:['table']});
      google.setOnLoadCallback(drawTable);
      var suiteTableInitFunctions = new Array();
      var suiteTableData = new Array();
    </script>
    <!--
      <script type="text/javascript" src="jquery-ui/js/jquery-ui-1.8.16.custom.min.js"></script>
     -->
  </head>

  <body>    <div class="top-banner-root">
      <span class="top-banner-title-font">Test results</span>
      <button id="button" class="button">Switch Retro Theme</button> <!-- button -->
      <br/>
      <span class="top-banner-font-1">1 suite, 1 failed test</span>
    </div> <!-- top-banner-root -->
    <div class="navigator-root">
      <div class="navigator-suite-header">
        <span>All suites</span>
        <a href="#" title="Collapse/expand all the suites" class="collapse-all-link">
          <img src="collapseall.gif" class="collapse-all-icon">
          </img> <!-- collapse-all-icon -->
        </a> <!-- collapse-all-link -->
      </div> <!-- navigator-suite-header -->
      <div class="suite">
        <div class="rounded-window">
          <div class="suite-header light-rounded-window-top">
            <a href="#" panel-name="suite-Surefire_suite" class="navigator-link">
              <span class="suite-name border-failed">Surefire suite</span>
            </a> <!-- navigator-link -->
          </div> <!-- suite-header light-rounded-window-top -->
          <div class="navigator-suite-content">
            <div class="suite-section-title">
              <span>Info</span>
            </div> <!-- suite-section-title -->
            <div class="suite-section-content">
              <ul>
                <li>
                  <a href="#" panel-name="test-xml-Surefire_suite" class="navigator-link ">
                    <span>[unset file name]</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="testlist-Surefire_suite" class="navigator-link ">
                    <span class="test-stats">1 test</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="group-Surefire_suite" class="navigator-link ">
                    <span>0 groups</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="times-Surefire_suite" class="navigator-link ">
                    <span>Times</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="reporter-Surefire_suite" class="navigator-link ">
                    <span>Reporter output</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="ignored-methods-Surefire_suite" class="navigator-link ">
                    <span>Ignored methods</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" panel-name="chronological-Surefire_suite" class="navigator-link ">
                    <span>Chronological view</span>
                  </a> <!-- navigator-link  -->
                </li>
              </ul>
            </div> <!-- suite-section-content -->
            <div class="result-section">
              <div class="suite-section-title">
                <span>Results</span>
              </div> <!-- suite-section-title -->
              <div class="suite-section-content">
                <ul>
                  <li>
                    <span class="method-stats">12 methods, 1 failed,   11 passed</span>
                  </li>
                  <li>
                    <span class="method-list-title failed">Failed methods</span>
                    <span class="show-or-hide-methods failed">
                      <a href="#" panel-name="suite-Surefire_suite" class="hide-methods failed suite-Surefire_suite"> (hide)</a> <!-- hide-methods failed suite-Surefire_suite -->
                      <a href="#" panel-name="suite-Surefire_suite" class="show-methods failed suite-Surefire_suite"> (show)</a> <!-- show-methods failed suite-Surefire_suite -->
                    </span>
                    <div class="method-list-content failed suite-Surefire_suite">
                      <span>
                        <img src="failed.png" width="3%"/>
                        <a href="#" panel-name="suite-Surefire_suite" title="tests.ConfigurationTests" class="method navigator-link" hash-for-method="testBrowserConfiguration">testBrowserConfiguration</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                    </div> <!-- method-list-content failed suite-Surefire_suite -->
                  </li>
                  <li>
                    <span class="method-list-title passed">Passed methods</span>
                    <span class="show-or-hide-methods passed">
                      <a href="#" panel-name="suite-Surefire_suite" class="hide-methods passed suite-Surefire_suite"> (hide)</a> <!-- hide-methods passed suite-Surefire_suite -->
                      <a href="#" panel-name="suite-Surefire_suite" class="show-methods passed suite-Surefire_suite"> (show)</a> <!-- show-methods passed suite-Surefire_suite -->
                    </span>
                    <div class="method-list-content passed suite-Surefire_suite">
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Surefire_suite" title="tests.FrameworkValidationTests" class="method navigator-link" hash-for-method="testConfigReaderUtility">testConfigReaderUtility</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Surefire_suite" title="tests.ConfigurationTests" class="method navigator-link" hash-for-method="testConfigurationLoading">testConfigurationLoading</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Surefire_suite" title="tests.FrameworkValidationTests" class="method navigator-link" hash-for-method="testDriverFactoryMethods">testDriverFactoryMethods</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Surefire_suite" title="tests.ConfigurationTests" class="method navigator-link" hash-for-method="testEnvironmentConfiguration">testEnvironmentConfiguration</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Surefire_suite" title="tests.FrameworkValidationTests" class="method navigator-link" hash-for-method="testFrameworkReadiness">testFrameworkReadiness</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Surefire_suite" title="tests.FrameworkValidationTests" class="method navigator-link" hash-for-method="testHomePagePOMStructure">testHomePagePOMStructure</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Surefire_suite" title="tests.FrameworkValidationTests" class="method navigator-link" hash-for-method="testMavenDependencies">testMavenDependencies</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Surefire_suite" title="tests.FrameworkValidationTests" class="method navigator-link" hash-for-method="testProjectStructure">testProjectStructure</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Surefire_suite" title="tests.ConfigurationTests" class="method navigator-link" hash-for-method="testSystemPropertiesOverride">testSystemPropertiesOverride</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Surefire_suite" title="tests.ConfigurationTests" class="method navigator-link" hash-for-method="testTestDataConfiguration">testTestDataConfiguration</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                      <span>
                        <img src="passed.png" width="3%"/>
                        <a href="#" panel-name="suite-Surefire_suite" title="tests.ConfigurationTests" class="method navigator-link" hash-for-method="testTimeoutConfiguration">testTimeoutConfiguration</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                    </div> <!-- method-list-content passed suite-Surefire_suite -->
                  </li>
                </ul>
              </div> <!-- suite-section-content -->
            </div> <!-- result-section -->
          </div> <!-- navigator-suite-content -->
        </div> <!-- rounded-window -->
      </div> <!-- suite -->
    </div> <!-- navigator-root -->
    <div class="wrapper">
      <div class="main-panel-root">
        <div panel-name="suite-Surefire_suite" class="panel Surefire_suite">
          <div class="suite-Surefire_suite-class-failed">
            <div class="main-panel-header rounded-window-top">
              <img src="failed.png"/>
              <span class="class-name">tests.ConfigurationTests</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="testBrowserConfiguration">
                  </a> <!-- testBrowserConfiguration -->
                  <span class="method-name">testBrowserConfiguration</span>
                  <div class="stack-trace">java.lang.AssertionError: Browser configuration should not be empty expected [false] but found [true]
	at tests.ConfigurationTests.testBrowserConfiguration(ConfigurationTests.java:28)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
... Removed 33 stack frames
</div> <!-- stack-trace -->
                  <em>
(Verify browser configuration)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-Surefire_suite-class-failed -->
          <div class="suite-Surefire_suite-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">tests.ConfigurationTests</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="testConfigurationLoading">
                  </a> <!-- testConfigurationLoading -->
                  <span class="method-name">testConfigurationLoading</span>
                  <em>
(Verify configuration properties are loaded)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testEnvironmentConfiguration">
                  </a> <!-- testEnvironmentConfiguration -->
                  <span class="method-name">testEnvironmentConfiguration</span>
                  <em>
(Verify environment configuration)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testSystemPropertiesOverride">
                  </a> <!-- testSystemPropertiesOverride -->
                  <span class="method-name">testSystemPropertiesOverride</span>
                  <em>
(Verify system properties override)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testTestDataConfiguration">
                  </a> <!-- testTestDataConfiguration -->
                  <span class="method-name">testTestDataConfiguration</span>
                  <em>
(Verify test data configuration)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testTimeoutConfiguration">
                  </a> <!-- testTimeoutConfiguration -->
                  <span class="method-name">testTimeoutConfiguration</span>
                  <em>
(Verify timeout configuration)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-Surefire_suite-class-passed -->
          <div class="suite-Surefire_suite-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">tests.FrameworkValidationTests</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="testConfigReaderUtility">
                  </a> <!-- testConfigReaderUtility -->
                  <span class="method-name">testConfigReaderUtility</span>
                  <em>
(Verify ConfigReader utility class)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testDriverFactoryMethods">
                  </a> <!-- testDriverFactoryMethods -->
                  <span class="method-name">testDriverFactoryMethods</span>
                  <em>
(Verify DriverFactory class methods)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testFrameworkReadiness">
                  </a> <!-- testFrameworkReadiness -->
                  <span class="method-name">testFrameworkReadiness</span>
                  <em>
(Verify test framework is ready)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testHomePagePOMStructure">
                  </a> <!-- testHomePagePOMStructure -->
                  <span class="method-name">testHomePagePOMStructure</span>
                  <em>
(Verify HomePage POM class structure)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testMavenDependencies">
                  </a> <!-- testMavenDependencies -->
                  <span class="method-name">testMavenDependencies</span>
                  <em>
(Verify Maven dependencies are available)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
              <div class="method">
                <div class="method-content">
                  <a name="testProjectStructure">
                  </a> <!-- testProjectStructure -->
                  <span class="method-name">testProjectStructure</span>
                  <em>
(Verify project structure)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-Surefire_suite-class-passed -->
        </div> <!-- panel Surefire_suite -->
        <div panel-name="test-xml-Surefire_suite" class="panel">
          <div class="main-panel-header rounded-window-top">
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <pre>
&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;
&lt;!DOCTYPE suite SYSTEM &quot;https://testng.org/testng-1.0.dtd&quot;&gt;
&lt;suite name=&quot;Surefire suite&quot; verbose=&quot;0&quot;&gt;
  &lt;test thread-count=&quot;5&quot; name=&quot;Surefire test&quot; verbose=&quot;0&quot;&gt;
    &lt;classes&gt;
      &lt;class name=&quot;tests.ConfigurationTests&quot;/&gt;
      &lt;class name=&quot;tests.FrameworkValidationTests&quot;/&gt;
    &lt;/classes&gt;
  &lt;/test&gt; &lt;!-- Surefire test --&gt;
&lt;/suite&gt; &lt;!-- Surefire suite --&gt;
            </pre>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="testlist-Surefire_suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Tests for Surefire suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <ul>
              <li>
                <span class="test-name">Surefire test (2 classes)</span>
              </li>
            </ul>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="group-Surefire_suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Groups for Surefire suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="times-Surefire_suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Times for Surefire suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="times-div">
              <script type="text/javascript">
suiteTableInitFunctions.push('tableData_Surefire_suite');
function tableData_Surefire_suite() {
var data = new google.visualization.DataTable();
data.addColumn('number', 'Number');
data.addColumn('string', 'Method');
data.addColumn('string', 'Class');
data.addColumn('number', 'Time (ms)');
data.addRows(12);
data.setCell(0, 0, 0)
data.setCell(0, 1, 'testMavenDependencies')
data.setCell(0, 2, 'tests.FrameworkValidationTests')
data.setCell(0, 3, 20);
data.setCell(1, 0, 1)
data.setCell(1, 1, 'testBrowserConfiguration')
data.setCell(1, 2, 'tests.ConfigurationTests')
data.setCell(1, 3, 10);
data.setCell(2, 0, 2)
data.setCell(2, 1, 'testConfigurationLoading')
data.setCell(2, 2, 'tests.ConfigurationTests')
data.setCell(2, 3, 9);
data.setCell(3, 0, 3)
data.setCell(3, 1, 'testConfigReaderUtility')
data.setCell(3, 2, 'tests.FrameworkValidationTests')
data.setCell(3, 3, 6);
data.setCell(4, 0, 4)
data.setCell(4, 1, 'testDriverFactoryMethods')
data.setCell(4, 2, 'tests.FrameworkValidationTests')
data.setCell(4, 3, 4);
data.setCell(5, 0, 5)
data.setCell(5, 1, 'testTimeoutConfiguration')
data.setCell(5, 2, 'tests.ConfigurationTests')
data.setCell(5, 3, 4);
data.setCell(6, 0, 6)
data.setCell(6, 1, 'testHomePagePOMStructure')
data.setCell(6, 2, 'tests.FrameworkValidationTests')
data.setCell(6, 3, 3);
data.setCell(7, 0, 7)
data.setCell(7, 1, 'testFrameworkReadiness')
data.setCell(7, 2, 'tests.FrameworkValidationTests')
data.setCell(7, 3, 3);
data.setCell(8, 0, 8)
data.setCell(8, 1, 'testEnvironmentConfiguration')
data.setCell(8, 2, 'tests.ConfigurationTests')
data.setCell(8, 3, 2);
data.setCell(9, 0, 9)
data.setCell(9, 1, 'testSystemPropertiesOverride')
data.setCell(9, 2, 'tests.ConfigurationTests')
data.setCell(9, 3, 2);
data.setCell(10, 0, 10)
data.setCell(10, 1, 'testProjectStructure')
data.setCell(10, 2, 'tests.FrameworkValidationTests')
data.setCell(10, 3, 1);
data.setCell(11, 0, 11)
data.setCell(11, 1, 'testTestDataConfiguration')
data.setCell(11, 2, 'tests.ConfigurationTests')
data.setCell(11, 3, 1);
window.suiteTableData['Surefire_suite']= { tableData: data, tableDiv: 'times-div-Surefire_suite'}
return data;
}
              </script>
              <span class="suite-total-time">Total running time: 65 ms</span>
              <div id="times-div-Surefire_suite">
              </div> <!-- times-div-Surefire_suite -->
            </div> <!-- times-div -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="reporter-Surefire_suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Reporter output for Surefire suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="ignored-methods-Surefire_suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">0 ignored methods</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="chronological-Surefire_suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Methods in chronological order</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="chronological-class">
              <div class="chronological-class-name">tests.ConfigurationTests</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">testConfigurationLoading</span>
                <span class="method-start">0 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <img src="failed.png">
                </img>
                <span class="method-name">testBrowserConfiguration</span>
                <span class="method-start">14 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testTimeoutConfiguration</span>
                <span class="method-start">31 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testEnvironmentConfiguration</span>
                <span class="method-start">37 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testTestDataConfiguration</span>
                <span class="method-start">40 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testSystemPropertiesOverride</span>
                <span class="method-start">43 ms</span>
              </div> <!-- test-method -->
            </div> <!-- chronological-class -->
            <div class="chronological-class">
              <div class="chronological-class-name">tests.FrameworkValidationTests</div> <!-- chronological-class-name -->
              <div class="test-method">
                <span class="method-name">testConfigReaderUtility</span>
                <span class="method-start">47 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testHomePagePOMStructure</span>
                <span class="method-start">55 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testDriverFactoryMethods</span>
                <span class="method-start">59 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testMavenDependencies</span>
                <span class="method-start">65 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testProjectStructure</span>
                <span class="method-start">88 ms</span>
              </div> <!-- test-method -->
              <div class="test-method">
                <span class="method-name">testFrameworkReadiness</span>
                <span class="method-start">90 ms</span>
              </div> <!-- test-method -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
      </div> <!-- main-panel-root -->
    </div> <!-- wrapper -->
  </body>
<script type="text/javascript" src="testng-reports2.js"></script>
</html>
