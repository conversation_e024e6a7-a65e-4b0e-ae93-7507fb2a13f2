package pages;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import utils.DriverFactory;

/**
 * HomePage Page Object Model class
 * Contains locators and methods for WeThinkCode homepage
 */
public class HomePage {
    
    private WebDriver driver;
    
    // Constructor
    public HomePage() {
        this.driver = DriverFactory.getDriver();
        PageFactory.initElements(driver, this);
    }
    
    // Page Elements
    @FindBy(xpath = "//a[contains(@href, '/apply') or contains(text(), 'Apply')]")
    private WebElement applyNowButton;
    
    @FindBy(xpath = "//a[contains(@href, '/about') or contains(text(), 'About')]")
    private WebElement aboutLink;
    
    @FindBy(xpath = "//a[contains(@href, '/contact') or contains(text(), 'Contact')]")
    private WebElement contactLink;
    
    @FindBy(xpath = "//a[contains(@href, '/courses') or contains(text(), 'Courses')]")
    private WebElement coursesLink;
    
    @FindBy(xpath = "//img[@alt='WeThinkCode_' or contains(@src, 'logo')]")
    private WebElement logo;
    
    @FindBy(xpath = "//h1 | //h2 | //div[contains(@class, 'hero')] | //div[contains(@class, 'banner')]")
    private WebElement heroSection;
    
    @FindBy(xpath = "//nav | //header | //div[contains(@class, 'nav')]")
    private WebElement navigationMenu;
    
    @FindBy(xpath = "//footer")
    private WebElement footer;
    
    // Page Methods
    
    /**
     * Click on Apply Now button
     */
    public void clickApplyNow() {
        try {
            DriverFactory.getWait().until(ExpectedConditions.elementToBeClickable(applyNowButton));
            applyNowButton.click();
            System.out.println("Clicked on Apply Now button");
        } catch (Exception e) {
            System.out.println("Apply Now button not found or not clickable: " + e.getMessage());
        }
    }
    
    /**
     * Click on About link
     */
    public void clickAbout() {
        try {
            DriverFactory.getWait().until(ExpectedConditions.elementToBeClickable(aboutLink));
            aboutLink.click();
            System.out.println("Clicked on About link");
        } catch (Exception e) {
            System.out.println("About link not found or not clickable: " + e.getMessage());
        }
    }
    
    /**
     * Click on Contact link
     */
    public void clickContact() {
        try {
            DriverFactory.getWait().until(ExpectedConditions.elementToBeClickable(contactLink));
            contactLink.click();
            System.out.println("Clicked on Contact link");
        } catch (Exception e) {
            System.out.println("Contact link not found or not clickable: " + e.getMessage());
        }
    }
    
    /**
     * Click on Courses link
     */
    public void clickCourses() {
        try {
            DriverFactory.getWait().until(ExpectedConditions.elementToBeClickable(coursesLink));
            coursesLink.click();
            System.out.println("Clicked on Courses link");
        } catch (Exception e) {
            System.out.println("Courses link not found or not clickable: " + e.getMessage());
        }
    }
    
    /**
     * Check if logo is displayed
     */
    public boolean isLogoDisplayed() {
        try {
            DriverFactory.getWait().until(ExpectedConditions.visibilityOf(logo));
            return logo.isDisplayed();
        } catch (Exception e) {
            System.out.println("Logo not found: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Check if hero section is displayed
     */
    public boolean isHeroSectionDisplayed() {
        try {
            DriverFactory.getWait().until(ExpectedConditions.visibilityOf(heroSection));
            return heroSection.isDisplayed();
        } catch (Exception e) {
            System.out.println("Hero section not found: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Check if navigation menu is displayed
     */
    public boolean isNavigationMenuDisplayed() {
        try {
            DriverFactory.getWait().until(ExpectedConditions.visibilityOf(navigationMenu));
            return navigationMenu.isDisplayed();
        } catch (Exception e) {
            System.out.println("Navigation menu not found: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Check if footer is displayed
     */
    public boolean isFooterDisplayed() {
        try {
            return footer.isDisplayed();
        } catch (Exception e) {
            System.out.println("Footer not found: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Get page title
     */
    public String getPageTitle() {
        return driver.getTitle();
    }
    
    /**
     * Get current URL
     */
    public String getCurrentUrl() {
        return driver.getCurrentUrl();
    }
    
    /**
     * Verify homepage is loaded
     */
    public boolean isHomePageLoaded() {
        try {
            // Wait for key elements to be present
            DriverFactory.getWait().until(ExpectedConditions.visibilityOf(navigationMenu));
            
            // Check if URL contains expected domain
            String currentUrl = getCurrentUrl();
            boolean urlCheck = currentUrl.contains("wethinkcode.co.za");
            
            // Check if title is not empty
            String title = getPageTitle();
            boolean titleCheck = title != null && !title.isEmpty();
            
            System.out.println("Homepage loaded - URL: " + currentUrl + ", Title: " + title);
            return urlCheck && titleCheck;
        } catch (Exception e) {
            System.out.println("Homepage not loaded properly: " + e.getMessage());
            return false;
        }
    }
}
