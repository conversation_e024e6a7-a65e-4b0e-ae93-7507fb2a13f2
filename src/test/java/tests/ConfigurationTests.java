package tests;

import org.testng.Assert;
import org.testng.annotations.Test;
import utils.ConfigReader;

/**
 * ConfigurationTests class to validate framework configuration
 * These tests don't require a browser and can run in any environment
 */
public class ConfigurationTests {
    
    @Test(priority = 1, description = "Verify configuration properties are loaded")
    public void testConfigurationLoading() {
        // Test base URL configuration
        String baseUrl = ConfigReader.getBaseUrl();
        Assert.assertNotNull(baseUrl, "Base URL should not be null");
        Assert.assertTrue(baseUrl.contains("wethinkcode.co.za"), 
            "Base URL should contain wethinkcode.co.za");
        
        System.out.println("✅ Base URL configured: " + baseUrl);
    }
    
    @Test(priority = 2, description = "Verify browser configuration")
    public void testBrowserConfiguration() {
        String browser = ConfigReader.getBrowser();
        Assert.assertNotNull(browser, "Browser configuration should not be null");
        Assert.assertFalse(browser.isEmpty(), "Browser configuration should not be empty");

        // Check if browser is one of the supported values
        boolean isValidBrowser = browser.equals("chrome") || browser.equals("firefox") || browser.equals("mobile");
        if (!isValidBrowser) {
            System.out.println("⚠️ Browser value '" + browser + "' is not in expected list, but configuration is loaded");
        } else {
            System.out.println("✅ Browser configured: " + browser);
        }
    }
    
    @Test(priority = 3, description = "Verify timeout configuration")
    public void testTimeoutConfiguration() {
        int timeout = ConfigReader.getTimeout();
        Assert.assertTrue(timeout > 0, "Timeout should be greater than 0");
        Assert.assertTrue(timeout <= 60, "Timeout should be reasonable (≤ 60 seconds)");
        
        System.out.println("✅ Timeout configured: " + timeout + " seconds");
    }
    
    @Test(priority = 4, description = "Verify environment configuration")
    public void testEnvironmentConfiguration() {
        String environment = ConfigReader.getEnvironment();
        Assert.assertNotNull(environment, "Environment should not be null");
        
        System.out.println("✅ Environment configured: " + environment);
    }
    
    @Test(priority = 5, description = "Verify test data configuration")
    public void testTestDataConfiguration() {
        String testEmail = ConfigReader.getProperty("test.email");
        String testName = ConfigReader.getProperty("test.name");
        String testPhone = ConfigReader.getProperty("test.phone");
        
        Assert.assertNotNull(testEmail, "Test email should be configured");
        Assert.assertTrue(testEmail.contains("@"), "Test email should be valid format");
        
        Assert.assertNotNull(testName, "Test name should be configured");
        Assert.assertFalse(testName.isEmpty(), "Test name should not be empty");
        
        Assert.assertNotNull(testPhone, "Test phone should be configured");
        
        System.out.println("✅ Test data configured:");
        System.out.println("   Email: " + testEmail);
        System.out.println("   Name: " + testName);
        System.out.println("   Phone: " + testPhone);
    }
    
    @Test(priority = 6, description = "Verify system properties override")
    public void testSystemPropertiesOverride() {
        // Test that system properties can override config file values
        String originalBrowser = ConfigReader.getBrowser();
        
        // Set system property
        System.setProperty("browser", "firefox");
        String overriddenBrowser = ConfigReader.getBrowser();
        
        Assert.assertEquals(overriddenBrowser, "firefox", 
            "System property should override config file value");
        
        // Clean up
        System.clearProperty("browser");
        
        System.out.println("✅ System property override working correctly");
        System.out.println("   Original: " + originalBrowser + " → Override: " + overriddenBrowser);
    }
}
