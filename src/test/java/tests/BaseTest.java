package tests;

import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Parameters;
import utils.ConfigReader;
import utils.DriverFactory;

/**
 * BaseTest class containing common setup and teardown methods
 */
public class BaseTest {
    
    @BeforeMethod
    @Parameters({"browser"})
    public void setUp(String browser) {
        // Use parameter if provided, otherwise use config
        String browserType = (browser != null) ? browser : ConfigReader.getBrowser();
        
        System.out.println("Starting test with browser: " + browserType);
        DriverFactory.initializeDriver(browserType);
        
        // Navigate to base URL
        DriverFactory.getDriver().get(ConfigReader.getBaseUrl());
    }
    
    @AfterMethod
    public void tearDown() {
        System.out.println("Closing browser...");
        DriverFactory.quitDriver();
    }
}
